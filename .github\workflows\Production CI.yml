name: Production CI

on:
  push:
    branches: [ "main" ]

jobs:
  build:

    runs-on: [self-hosted, Linux, X64, autoscaled-ec2]
    permissions:
      contents: read
      actions: read
      statuses: write
    strategy:
      matrix:
        node-version: [18.x ]
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

##############################################################################################
################################ Production CI ###############################################
##############################################################################################
    - name:
        Deploy to portal.quartermaster.us - Navigate to Directory, Reset and Pull Changes
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to quartermaster-webapp"
        cd ~/app/
        git restore .
        git pull

    - name:
        Deploy to portal.quartermaster.us - Install Backend Dependencies
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/
        npm install

    - name:
        Deploy to portal.quartermaster.us - Install Frontend Dependencies
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/frontend/
        npm install

    - name:
        Deploy to portal.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/frontend/
        rm -rf .env
        touch .env

    - name:
        Deploy to portal.quartermaster.us - Populate Frontend .env File with Secrets
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/frontend/
        echo "${{ secrets.PORTAL_QUARTERMASTER_US_FRONTEND }}" > .env

    - name:
        Deploy to portal.quartermaster.us - Build Frontend
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/frontend/
        npm run build

    - name:
        Deploy to portal.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/
        rm -rf .env
        touch .env

    - name:
        Deploy to portal.quartermaster.us - Populate Backend .env File with Secrets
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/app/
        echo "${{ secrets.PORTAL_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to portal.quartermaster.us - Restart PM2 Process
      if: github.ref == 'refs/heads/main'
      run: |
        pm2 restart 'Quartermaster Webapp'
    
##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
    - name: Set URL based on branch
      run: |
        echo "URL=https://infra.quartermaster.us" >> $GITHUB_ENV

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nProduction CI (Autoscale)\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nProduction CI (Autoscale)\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_CHAT }}