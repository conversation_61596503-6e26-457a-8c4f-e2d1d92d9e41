const express = require("express");
const { validateData } = require("../../middlewares/validator");
const { body } = require("express-validator");
const { validateError, getUnitIdsFromVessel, canAccessVessel, getLocationsCollections, getTimestampsFromVessel } = require("../../utils/functions");
const limitPromise = require("../../modules/pLimit");
const { default: mongoose } = require("mongoose");
const isAuthenticated = require("../../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../../middlewares/assignEndpointId");
const { endpointIds } = require("../../utils/endpointIds");
const db = require("../../modules/db");
const router = express.Router();
const compression = require("compression");
const vesselService = require("../../services/Vessel.service");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_V2),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("lastKnown")
                    .customSanitizer((v) => Number(v))
                    .isInt({ min: 0, max: 1 })
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v) => v.map((id) => mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselId } = req.params;
            const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body;
            console.log(`/v2/vesselLocations ${vesselId}`, startTimestamp, endTimestamp, lastKnown);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access coordinates for vessel '${vesselId}'` });
            }

            const historicalUnitIds = getUnitIdsFromVessel(vessel);
            if (historicalUnitIds.length === 0) return res.status(400).json({ message: "Vessel has no associated unit_id in history" });

            const ts = new Date().getTime();

            const timestamps = getTimestampsFromVessel(vessel);
            const collections = await getLocationsCollections(db.locationsOptimized, timestamps.startTimestamp, timestamps.endTimestamp);
            const query = { "metadata.onboardVesselId": mongoose.Types.ObjectId(vesselId) };

            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };

            const locations = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/v2/vesselLocations ${vesselId} querying DB`);

                if (lastKnown) {
                    const locationPromises = collections.map((collection) =>
                        collection.aggregate([
                            {
                                $match: { "metadata.onboardVesselId": mongoose.Types.ObjectId(vesselId) }
                            },
                            { $limit: 1 },
                            {
                                $project: {
                                    _id: 1,
                                    timestamp: 1,
                                    groundSpeed: 1,
                                    isStationary: 1,
                                    latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                                    longitude: { $arrayElemAt: ["$location.coordinates", 0] }
                                }
                            }
                        ]).toArray()
                    );
                    const allLocations = (await Promise.all(locationPromises)).filter(Boolean);

                    if (allLocations.length === 0) return null;

                    return allLocations.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
                } else {
                    const resultPromises = collections.map((collection) => {
                        const cursor = collection.aggregate([
                            { $match: query },
                            { $project: {
                                _id: 1,
                                timestamp: 1,
                                groundSpeed: 1,
                                isStationary: 1,
                                latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                                longitude: { $arrayElemAt: ["$location.coordinates", 0] }
                            }}
                        ]);

                        if (isSwagger) {
                            cursor.limit(20);
                        }

                        return cursor.toArray();
                    });

                    const allResults = await Promise.all(resultPromises);
                    const flattenedResults = allResults.flat();

                    return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                }
            });

            console.log(`/v2/vesselLocations ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/v2/vesselLocations ${vesselId} received ${(locations && locations.length) || 1} coordinates`);
            console.log(`/v2/vesselLocations ${vesselId} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: Ground speed in knots
 *           example: 12.5
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp of the location record
 *           example: "2023-06-16T12:00:00.000Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */

/**
 * @swagger
 * /v2/vesselLocations/{vesselId}:
 *   post:
 *     summary: Fetch vessel location data
 *     description: Fetches location data for a specific vessel, with options for historical data or last known position
 *     tags: [Vessel Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: vesselId
 *         in: path
 *         required: true
 *         description: The ID of the vessel to fetch locations for
 *         schema:
 *           type: string
 *           example: 683df46b073245cf0fd62bb9
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 default: 0
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1727136000000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1727222400000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - description: Array of locations when lastKnown=0 or not provided
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/VesselLocation'
 *                 - allOf:
 *                   - description: Single location object when lastKnown=1
 *                   - $ref: '#/components/schemas/VesselLocation'
 *                 - description: When no locations are found
 *                   type: "null"
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */

module.exports = router;
