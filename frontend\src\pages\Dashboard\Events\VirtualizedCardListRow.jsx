import { memo } from "react";
import { Grid } from "@mui/material";
import Card from "./Card";
import GroupedCard from "./GroupedCard";

const VirtualizedCardListRow = ({ index, style, data }) => {
    const { items, columnCount, CustomCard, setShowDetailModal, setSelectedCard, onFlaggedByClick, buttonsToShow, signedUrls } = data;
    const startIndex = index * columnCount;

    return (
        <Grid container style={style} spacing={2} paddingBottom={2}>
            {Array.from({ length: columnCount }).map((_, i) => {
                const eventIndex = startIndex + i;
                if (eventIndex >= items.length) return null;

                const artifact = items[eventIndex];
                const CardComponent = CustomCard || (artifact.isGroup ? GroupedCard : Card);

                return (
                    <Grid
                        key={artifact._id || artifact.artifact?._id}
                        size={{
                            xs: 12,
                            sm: 6,
                            md: 4,
                            lg: 3,
                            xl: 2.4,
                        }}
                    >
                        <CardComponent
                            card={artifact}
                            setShowDetailModal={setShowDetailModal}
                            setSelectedCard={setSelectedCard}
                            onFlaggedByClick={onFlaggedByClick}
                            buttonsToShow={buttonsToShow}
                            signedUrls={signedUrls}
                        />
                    </Grid>
                );
            })}
        </Grid>
    );
};

VirtualizedCardListRow.displayName = "VirtualizedCardListRow";
export default memo(VirtualizedCardListRow);
