const { validateData } = require("../middlewares/validator");
const { query } = require("express-validator");
const dayjs = require("dayjs");
const express = require("express");
const awsKinesis = require("../modules/awsKinesis");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const {
    validateError,
    canAccessVessel,
    fileNameTimestamp,
    generateZip,
    removeSpecialCharsFromFilename,
    userHasPermissions,
    getLocationsCollections,
} = require("../utils/functions");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const Region = require("../models/Region");
const vesselService = require("../services/Vessel.service");
// const regionGroupService = require("../services/RegionGroup.service");
const db = require("../modules/db");
const { defaultDateTimeFormat } = require("../utils/timezonesList");
const { permissions } = require("../utils/permissions");
const streamService = require("../services/Stream.service");
const router = express.Router();
// const { Readable } = require("node:stream");
// const { getObjectStream } = require("../modules/awsS3");

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

router.get(
    "/listStreams",
    assignEndpointId.bind(this, endpointIds.FETCH_STREAMS_LIST),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { region } = req.query;

            let streams = [];

            if (region) {
                streams = await awsKinesis.listStreams({ region });
            } else {
                const awsRegions = (await Region.find()).filter((r) => r.is_live);

                const allStreams = await Promise.all(
                    awsRegions.map(async (region) => {
                        try {
                            return await awsKinesis.listStreams({ region: region.value });
                        } catch {
                            return [];
                        }
                    }),
                );

                streams = allStreams.flat();
            }

            // const [regionGroups, vessels] = await Promise.all([
            //     regionGroupService.find(),
            //     vesselService.find({}, { unit_id: 1, name: 1, _id: 1, is_active: 1, thumbnail_compressed_s3_key: 1 }),
            // ]);
            const vessels = await vesselService.find(
                {},
                { unit_id: 1, name: 1, _id: 1, is_active: 1, thumbnail_compressed_s3_key: 1, region_group_id: 1 },
            );

            // filter provisioned streams
            streams = streams.filter((stream) => {
                const vessel = vessels.find((v) => v.unit_id === stream.StreamName);
                if (vessel) return canAccessVessel(req, vessel);
                else if (req.user && userHasPermissions(req.user, [permissions.accessAllVessels])) return true;
                else return false;
            });

            streams = streams
                .map((stream) => {
                    const vessel = vessels.find((v) => v.unit_id === stream.StreamName);
                    // const regionGroup = vessel ? regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString())) : null;

                    return {
                        ...stream,
                        // RegionGroupId: regionGroup ? regionGroup._id.toString() : null,
                        RegionGroupId: vessel ? vessel.region_group_id : null,
                        VesselId: vessel ? vessel._id.toString() : null,
                        VesselIsActive: vessel ? vessel.is_active : null,
                        ThumbnailS3Key: vessel ? vessel.thumbnail_compressed_s3_key : null,
                        VesselName: vessel ? vessel.name : null,
                    };
                })
                .filter(Boolean);

            res.json(streams);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/dashStreamingSessionURL",
    assignEndpointId.bind(this, endpointIds.FETCH_STREAM_DASH_URL),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("streamMode")
                    .isString()
                    .notEmpty()
                    .toUpperCase()
                    .isIn(["LIVE", "ON_DEMAND"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("minutes")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, streamMode, minutes } = req.query;

            const stream = await streamService.fetchSingle({ unitId: streamName });
            if (!stream) return res.status(404).json({ message: "Stream does not exist" });

            if (req.api_key || (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels]))) {
                const vessel = await vesselService.findByAssignedUnitId({ unitId: stream.unit_id });
                if (!vessel) return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                if (!canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                }
            }

            const url = await awsKinesis.getDashStreamingSessionURL({ streamName, region, streamMode, minutes });

            res.json({ url: url });
        } catch (err) {
            validateError(err, res);
        }
    },
);

const responseWithZipMediaData = async (res, region, streamName, startTime, endTime, targetTimestamp, extension, data, user) => {
    try {
        const tags = await awsKinesis.getStreamTags({ region, streamName });

        const filenameMask = `${fileNameTimestamp()} - ${removeSpecialCharsFromFilename((tags?.Name || streamName).replace(/ /g, "_"))}`;

        const collections = await getLocationsCollections(db.locationsRaw, startTime, endTime);

        const locationPromises = await Promise.all(collections.map((collection) =>
            collection.aggregate(
                [
                    { $match: { timestamp: { $gt: new Date(startTime), $lt: new Date(endTime) } } },
                    { $project: { _id: 1, latitude: { $arrayElemAt: ["$location.coordinates", 1] }, longitude: { $arrayElemAt: ["$location.coordinates", 0] }, timestamp: 1, groundSpeed: 1, isStationary: 1 } },
                    { $sort: { timestamp: 1 } },
                    { $limit: 1 },
                ])
        ));
        const allLocations = locationPromises.flat();
        let vesselLocation = allLocations.find((loc) => loc.timestamp >= targetTimestamp);

        if (!vesselLocation) {
            for (const collection of collections.reverse()) {
                const lastLocations = await collection.aggregate([
                    { $project: { _id: 1, latitude: { $arrayElemAt: ["$location.coordinates", 1] }, longitude: { $arrayElemAt: ["$location.coordinates", 0] }, timestamp: 1, groundSpeed: 1, isStationary: 1 } },
                    { $sort: { timestamp: -1 } },
                    { $limit: 1 },
                ]).toArray();
                if (lastLocations.length > 0) {
                    vesselLocation = lastLocations[0];
                    break; // Found a location, stop searching
                }
            }
        }

        const metadata = `
            Vessel Name: ${tags?.Name}
            Location: ${vesselLocation?.latitude}${vesselLocation?.latitude ? "," : ""}${vesselLocation?.longitude}
            Timestamp: ${dayjs(Number(targetTimestamp)).format(user.date_time_format || defaultDateTimeFormat)}
            Ground Speed: ${vesselLocation?.groundSpeed} Knots
        `;

        const filesData = [
            {
                name: `${filenameMask}.${extension}`,
                content: data, //clipResponse.Payload,
            },
            {
                name: `${filenameMask}.txt`,
                content: metadata,
            },
        ];

        const zip = generateZip(filesData);
        const stream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true });

        //Using pipeline for better error handling.
        res.setHeader("Content-Type", "application/zip");
        res.setHeader("Content-Disposition", `attachment; filename="${filenameMask}.zip"`);

        await new Promise((resolve, reject) => {
            stream.on("error", (err) => {
                console.error("stream error:", err);
                reject(err);
            });

            stream
                .pipe(res)
                .on("finish", resolve)
                .on("error", (e) => {
                    reject(e);
                });
        });
    } catch (error) {
        if (!res.headersSent) {
            console.error(`PROC MEDIA ERROR: ${error.message}`);
            res.status(500).json({ message: "Internal server error" });
        }
        res.end();
    }
};

router.get(
    "/getScreenShot",
    assignEndpointId.bind(this, endpointIds.GET_SCREENSHOT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("timestamp")
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, timestamp: qTimestamp } = req.query;

            if (req.api_key || (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels]))) {
                const vessel = await vesselService.findByAssignedUnitId({ unitId: streamName });
                if (!vessel) return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                if (!canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                }
            }

            const timestamp = qTimestamp ? new Date(qTimestamp).getTime() : Date.now();
            const extension = "jpeg";
            const screenshotData = await awsKinesis.getScreenshot(streamName, region, timestamp, extension.toUpperCase());

            if (screenshotData) {
                await responseWithZipMediaData(res, region, streamName, timestamp, timestamp + 10000, timestamp, extension, screenshotData, req.user);
            } else {
                if (!res.headersSent) {
                    res.status(500).json({ message: "Cant process the request" });
                }
                res.end();
            }
        } catch (error) {
            console.error("Error in get-screenshot route:", error);
            if (!res.headersSent) {
                if (error.name === "ScreenshotNotFoundError" || error.name === "ResourceNotFoundException") {
                    res.status(404).json({ message: "Sorry, image not found for the selected timestamp. Please try again." });
                } else if (error.name === "InvalidArgumentException") {
                    res.status(400).json({ message: "Invalid parameters" });
                } else if (error.name === "NotAuthorizedException") {
                    res.status(403).json({ message: "Not authorized to access the stream." });
                } else {
                    res.status(500).json({ message: "Internal server error" });
                }
            }
            res.end();
        }
    },
);

router.get(
    "/getClip",
    assignEndpointId.bind(this, endpointIds.GET_CLIP),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("timestamp")
                    .optional()
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, timestamp } = req.query;

            if (req.api_key || (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels]))) {
                const vessel = await vesselService.findByAssignedUnitId({ unitId: streamName });
                if (!vessel) return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                if (!canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                }
            }

            let targetTimestamp = timestamp ? new Date(timestamp).getTime() : Date.now();

            // take the window in scope 30 seconds before target timestamp and 30 seconds after
            const offsetBefore = 30000;
            const offsetAfter = 30000;

            const startTime = targetTimestamp - offsetBefore;
            const endTime = targetTimestamp + offsetAfter;

            const clipResponse = await awsKinesis.getClip(streamName, region, startTime, endTime);

            if (clipResponse.Payload instanceof Buffer) {
                const extension = clipResponse.ContentType.split("/").pop();
                await responseWithZipMediaData(
                    res,
                    region,
                    streamName,
                    startTime,
                    endTime,
                    targetTimestamp,
                    extension,
                    clipResponse.Payload,
                    req.user,
                );
            } else {
                console.warn("Unexpected Payload type: ", typeof clipResponse.Payload);
                if (!res.headersSent) {
                    res.status(500).json({ message: "Unexpected payload type from Streams Service" });
                }
                res.end();
            }
        } catch (error) {
            console.error("Error in get-clip route:", error);
            if (!res.headersSent) {
                if (error.name === "ResourceNotFoundException") {
                    res.status(404).json({ message: "Sorry, clip not found for the selected timestamp. Please try again." });
                } else if (error.name === "InvalidArgumentException") {
                    res.status(400).json({ message: "Invalid parameters" });
                } else if (error.name === "NotAuthorizedException") {
                    res.status(403).json({ message: "Not authorized to access the stream." });
                } else {
                    res.status(400).json({ message: error.message });
                }
            }
            res.end();
        }
    },
);

router.get(
    "/hlsStreamingSessionURL",
    assignEndpointId.bind(this, endpointIds.FETCH_STREAM_URL),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("streamMode")
                    .isString()
                    .notEmpty()
                    .toUpperCase()
                    .isIn(["LIVE", "ON_DEMAND"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("minutes")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, streamMode, minutes } = req.query;

            const stream = await streamService.fetchSingle({ unitId: streamName });
            if (!stream) return res.status(404).json({ message: "Stream does not exist" });

            if (req.api_key || (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels]))) {
                const vessel = await vesselService.findByAssignedUnitId({ unitId: stream.unit_id });
                if (!vessel) return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                if (!canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                }
            }

            const url = await awsKinesis.getHlsStreamingSessionURL({ streamName, region, streamMode, minutes });

            res.json({ url: url });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Streams
 *   description: Get data regarding streams
 * components:
 *   schemas:
 *     Stream:
 *       type: object
 *       properties:
 *         DeviceName:
 *           type: string
 *           nullable: true
 *           example: null
 *         StreamName:
 *           type: string
 *           description: The name of the stream.
 *           example: "prototype-24"
 *         StreamARN:
 *           type: string
 *           description: The Amazon Resource Name (ARN) of the stream.
 *           example: "arn:aws:kinesisvideo:ap-southeast-1:123456789012:stream/prototype-24/1724069687123"
 *         MediaType:
 *           type: string
 *           nullable: true
 *           example: null
 *         KmsKeyId:
 *           type: string
 *           description: The KMS key ID used for encryption.
 *           example: "arn:aws:kms:ap-southeast-1:123456789012:alias/aws/kinesisvideo"
 *         Version:
 *           type: string
 *           description: The version of the stream.
 *           example: "ww1ljAmwqPscFzFHDxRO"
 *         Status:
 *           type: string
 *           enum:
 *             - ACTIVE
 *             - INACTIVE
 *           description: The current status of the stream.
 *           example: "ACTIVE"
 *         CreationTime:
 *           type: string
 *           format: date-time
 *           description: The time when the stream was created.
 *           example: "2024-08-19T12:14:47.123Z"
 *         DataRetentionInHours:
 *           type: integer
 *           description: The data retention period in hours.
 *           example: 720
 *         Tags:
 *           type: object
 *           deprecated: true
 *           description: The tags associated with the stream.
 *           properties:
 *             Name:
 *               type: string
 *               example: BRP Malapascua MRRV-4403
 *             Thumbnail:
 *               type: string
 *               example: https://portal.quartermaster.us/4403.jpg
 *           example: {
 *             "Name": "BRP Malapascua MRRV-4403",
 *             "Thumbnail": "https://portal.quartermaster.us/4403.jpg"
 *           }
 *         IsLive:
 *           type: boolean
 *           description: Indicates whether the stream is live.
 *           example: false
 *         Region:
 *           type: string
 *           description: The region of the stream.
 *           example: "us-east-2"
 *         RegionGroupId:
 *           type: string
 *           nullable: true
 *           description: The region group of the stream.
 *           example: "66e000000000000000000000"
 *         VesselId:
 *           type: string
 *           nullable: true
 *           description: The vessel id of the stream.
 *           example: "66e000000000000000000000"
 *         VesselIsActive:
 *           type: boolean
 *           nullable: true
 *           description: "Whether the vessel associated with this stream is active."
 *           example: true
 *         ThumbnailS3Key:
 *           type: string
 *           nullable: true
 *           description: "Whether the vessel associated with this stream is active."
 *           example: true
 *         VesselName:
 *           type: string
 *           nullable: true
 *           description: The name of the vessel associated with this stream.
 *           example: "BRP Malapascua MRRV-4403"
 */

/**
 * @swagger
 * /kinesis/listStreams:
 *   get:
 *     summary: List all available Kinesis streams
 *     description: Fetches a list of all Kinesis video streams from specified region or all regions if no region is specified.
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: region
 *         in: query
 *         required: false
 *         description: AWS region to fetch streams from. If not provided, streams from all active regions will be fetched.
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *     responses:
 *       200:
 *         description: A list of streams
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Stream'
 *       400:
 *         description: Invalid region provided
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /kinesis/hlsStreamingSessionURL:
 *   get:
 *     summary: Get HLS streaming session URL
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: streamMode
 *         in: query
 *         required: true
 *         description: The mode of the stream, either 'LIVE' or 'ON_DEMAND'
 *         schema:
 *           type: string
 *           enum: [LIVE, ON_DEMAND]
 *       - name: minutes
 *         in: query
 *         required: false
 *         description: The interval for which replay is fetched
 *         schema:
 *           type: number
 *           example: 60
 *     responses:
 *       200:
 *         description: The HLS streaming session URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The HLS streaming session URL
 *       400:
 *         description: Invalid parameters provided
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /kinesis/dashStreamingSessionURL:
 *   get:
 *     summary: Get Dash streaming session URL
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: streamMode
 *         in: query
 *         required: true
 *         description: The mode of the stream, either 'LIVE' or 'ON_DEMAND'
 *         schema:
 *           type: string
 *           enum: [LIVE, ON_DEMAND]
 *       - name: minutes
 *         in: query
 *         required: false
 *         description: The interval for which replay is fetched
 *         schema:
 *           type: number
 *           example: 60
 *     responses:
 *       200:
 *         description: The HLS streaming session URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The HLS streaming session URL
 *       400:
 *         description: Invalid parameters provided
 *       500:
 *         description: Server error
 */
