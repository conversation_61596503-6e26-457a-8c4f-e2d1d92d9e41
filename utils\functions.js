const jwt = require("jsonwebtoken");
const { permissions } = require("./permissions");
const JSZip = require("jszip");
const simplify = require("simplify-js");
const dayjs = require("dayjs");
const isSameOrBefore = require("dayjs/plugin/isSameOrBefore");
dayjs.extend(isSameOrBefore);

const isURL = (value) => {
    const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;

    if (urlRegex.test(value)) return true;

    return false;
};

const isBase64 = (value) => {
    const base64Regex = /^(data:image\/[a-zA-Z]+;base64,){1}[^\s]+$/;

    if (base64Regex.test(value)) return true;

    return false;
};

const isBase64OrURL = (value) => {
    if (Array.isArray(value)) {
        if (value.every((v) => isBase64(v) || isURL(v))) {
            return true;
        }
    } else {
        if (isBase64(value) || isURL(value)) {
            return true;
        }
    }

    return false;
};

const isIntStrict = (value) => {
    if (typeof value !== "number" || !Number.isInteger(value)) return false;
    else return true;
};

const validateError = (err, res) => {
    if (err.name === "MongoServerError") {
        if (err.code === 11000) {
            res.status(400).json({
                message: `Value already exists: ${Object.keys(err.keyValue)
                    .map((key) => `${key} = ${err.keyValue[key]}`)
                    .join(", ")}`,
            });
        } else {
            console.error(err);
            res.status(500).json({ message: `Unexpected error occured: ${err?.message || JSON.stringify(err)}` });
        }
    } else if (err.code === "ResourceNotFoundException" || err.message === "No streams found in the specified timestamp range.") {
        res.status(404).json({ message: err.message });
    } else if (err.status) {
        res.status(err.status).json({ message: err?.message });
    } else {
        console.error(err);
        res.status(500).json({ message: `Unexpected error occured: ${err?.message || JSON.stringify(err)}` });
    }
};

// the coordinates must be sorted by timestamp in ascending order
const getSessionsByCoordinates = (coordinates) => {
    const sessions = [];

    let currentSession = [coordinates[0]];

    for (let i = 1; i < coordinates.length; i++) {
        if (new Date(coordinates[i].timestamp).getTime() - new Date(coordinates[i - 1].timestamp).getTime() < 300000) {
            currentSession.push(coordinates[i]);
        } else {
            if (currentSession.length > 1) sessions.push(currentSession);
            currentSession = [coordinates[i]];
        }
    }

    // Add the last session
    if (currentSession.length && currentSession.length > 1) {
        sessions.push(currentSession);
    }

    return sessions;
};

function generateInvitationLink(email, role_id, allowed_vessels, admin_id, role, organization_name, organization_id) {
    const payload = { email, role_id, allowed_vessels, admin_id, role, organization_name, organization_id, timestamp: Date.now() };
    const secretKey = process.env.JWT_SECRET;
    const token = jwt.sign(payload, secretKey, { expiresIn: "72h" });
    return token;
}

function generateUnsubscribeToken(email, notification_id) {
    const payload = { email, notification_id, timestamp: Date.now() };
    const secretKey = process.env.JWT_SECRET;
    const token = jwt.sign(payload, secretKey, { expiresIn: "90d" });
    return token;
}

function canAccessVessel({ user, api_key }, { _id: vessel_id, is_active }) {
    if (!vessel_id) throw new Error("vessel _id is required");
    if (is_active === undefined) throw new Error("vessel is_active is required");
    if (!user && !api_key) throw new Error("User or api_key is a required object");

    // console.log("canAccessVessel", vessel_id, is_active);

    if (user) {
        if (!user.permissions || !Array.isArray(user.permissions)) throw new Error("User permissions is a required array");
        if (userHasPermissions(user, [permissions.accessAllVessels])) return true;
    }

    if (!is_active) return false;

    const requester = user || api_key;

    if (!requester.allowed_vessels || !Array.isArray(requester.allowed_vessels)) throw new Error("allowed_vessels is a required array");

    const allowedVessels = requester.allowed_vessels;

    // console.log("allowedVessels", allowedVessels);
    // console.log("vessel_id", vessel_id);

    const isAllowed = allowedVessels.find((v) => v.toString() === vessel_id.toString()) ? true : false;

    return isAllowed;
}

const generateZip = function (filesData) {
    const zip = new JSZip();

    filesData.forEach((file) => {
        zip.file(removeSpecialCharsFromFilename(file.name), file.content);
    });

    return zip;
    //return await zip.generateAsync({ type: "blob" }); //.then(function(content) {
};

const fileNameTimestamp = () => {
    const date = new Date();

    return `${date.getUTCFullYear()}${date.getUTCMonth() + 1}${date.getUTCDate()}_${date.getUTCHours()}${date.getUTCMinutes()}${date.getUTCSeconds()}`;
};

const removeSpecialCharsFromFilename = function (filename) {
    const regex = /[^\w.\-_\p{L}\p{N}]/gu;
    const cleanedFilename = filename.replace(regex, "");
    const trimmedFilename = cleanedFilename.replace(/^[.\-_]+|[.\-_]+$/g, "");
    const normalizedFilename = trimmedFilename.replace(/([.\-_])\1+/g, "$1");
    if (!normalizedFilename) {
        return "unnamed_file";
    }

    return normalizedFilename;
};

const userHasPermissions = ({ permissions }, permissionIds, op = "AND") => {
    if (!permissions || !Array.isArray(permissions)) throw new Error("User permissions is a required array");
    if (!permissionIds || !Array.isArray(permissionIds)) throw new Error("Permission IDs is a required array");

    if (op === "AND") {
        return permissionIds.every((p_id) => permissions.find((p) => p.permission_id === p_id));
    } else {
        return permissionIds.some((p_id) => permissions.find((p) => p.permission_id === p_id));
    }
};

const streamToBuffer = async (readStream) => {
    return new Promise((resolve, reject) => {
        const chunks = [];

        readStream.on("data", (chunk) => {
            chunks.push(chunk);
        });

        readStream.on("end", () => {
            resolve(Buffer.concat(chunks).buffer);
        });

        readStream.on("error", (err) => {
            reject(err);
        });
    });
};

async function getStaticMapOld(markers = [], center = null, format = "png") {
    try {
        const baseUrl = "https://maps.googleapis.com/maps/api/staticmap";

        const args = {
            // zoom: "3",
            scale: "2",
            size: "800x400",
            maptype: "terrain", // satellite // roadmap // hybrid
            format: format || "png",
            key: process.env.GOOGLE_API_KEY,
        };

        // set center point for map or use auto adjust of the borders basing on the markers
        if (center) {
            args.center = `${center.latitude},${center.longitude}`;
        } else {
            args.auto = "";
        }

        const params = new URLSearchParams(args);

        const url = `${baseUrl}?${params.toString()}&${markers.map((marker) => `markers=${encodeURIComponent(marker)}`).join("&")}`;
        const response = await fetch(url);

        if (!response.ok) {
            // Handle HTTP errors (e.g., 400, 403, 500)
            const errorText = await response.text(); // Get error message from response
            throw new Error(`Static Map API request failed: ${response.status} - ${errorText}`);
        }
        return {
            mimeType: "image/png",
            source: response,
        };
    } catch (error) {
        console.error("Error fetching static map:", error);
        return {};
    }
}
// https://staticmapmaker.com/google/

// sizes: tiny / small / mid /
// colors: black, brown, green, purple, yellow, blue, gray, orange, red, white or hex representation
function buildStaticMarkerSignature(latitude, longitude, label = "1", size = "small", color = "red") {
    return `size:${size}|color:${color}|label:${label}|${latitude}, ${longitude}`;
    // icon: XXX
}

function generateTimeSeries(startTimestamp, endTimestamp, interval = 1) {
    let start = new Date(startTimestamp).setSeconds(0, 0);
    let end = new Date(endTimestamp).setSeconds(0, 0);
    const result = {};

    let current = new Date(start);
    while (current <= end) {
        result[current.toISOString()] = 0;
        current.setUTCMinutes(current.getUTCMinutes() + interval);
    }

    return result;
}

const escapeRegExp = (string) => {
    return string.replace(/[-[\]/{}()*+?.\\^$|]/g, "\\$&");
};

const getUnitIdsFromVessel = (vessel) => {
    if (!vessel) throw new Error("Vessel is required");
    if (!vessel.units_history || !Array.isArray(vessel.units_history)) throw new Error("Vessel units_history is required and must be an array");
    const unitIds = vessel.units_history.map((v) => v.unit_id).filter(Boolean);
    const removeDuplicates = [...new Set(unitIds)];
    return removeDuplicates;
};

const getTimestampsFromVessel = ({ units_history } = {}) => {
    if (!Array.isArray(units_history)) throw new Error("units_history must be an array");

    let start = Infinity, end = -Infinity;
    for (const { mount_timestamp: m, unmount_timestamp: u } of units_history) {
        if (m) start = Math.min(start, new Date(m).getTime());
        if (u) end = Math.max(end, new Date(u).getTime());
    }

    if (!isFinite(start)) throw new Error("No valid mount_timestamp found");
    if (end === -Infinity) end = Date.now();

    return { startTimestamp: new Date(start).toISOString(), endTimestamp: new Date(end).toISOString() };
};


function getContentTypeFromFileExtension(fileExtension) {
    const fileType = fileExtension;
    if (
        fileType === "jpg" ||
        fileType === "jpeg" ||
        fileType === "png" ||
        fileType === "gif" ||
        fileType === "bmp" ||
        fileType === "tiff" ||
        fileType === "ico" ||
        fileType === "webp"
    ) {
        return `image/${fileType}`;
    } else if (
        fileType === "mp4" ||
        fileType === "mov" ||
        fileType === "avi" ||
        fileType === "wmv" ||
        fileType === "flv" ||
        fileType === "mkv" ||
        fileType === "webm"
    ) {
        return `video/${fileType}`;
    }
    return "application/octet-stream";
}
// This is used for capitalizing names in the database for group region name
function normalizeName(name) {
    return name.trim().replace(/\s+/g, " ");
}

function findVesselByUnitHistory(vessels, from, to, unit_id) {
    const fromTs = typeof from === "number" ? from : new Date(from).getTime();
    const toTs = typeof to === "number" ? to : new Date(to).getTime();
    for (const vessel of vessels) {
        if (!Array.isArray(vessel.units_history)) continue;
        for (const history of vessel.units_history) {
            const historyFrom = typeof history.mount_timestamp === "number" ? history.mount_timestamp : new Date(history.mount_timestamp).getTime();
            const historyTo =
                history.unmount_timestamp === undefined || history.unmount_timestamp === null
                    ? null
                    : typeof history.unmount_timestamp === "number"
                      ? history.unmount_timestamp
                      : new Date(history.unmount_timestamp).getTime();
            if (history.unit_id === unit_id && historyFrom <= toTs && (historyTo === null || historyTo >= fromTs)) {
                return { vessel, unit_id: history.unit_id };
            }
        }
    }
    return null;
}

const groupArtifactsByDuplicateIndex = (artifacts, threshold = 0.7) => {
    const processed = new Set();
    const groups = [];

    for (let i = 0; i < artifacts.length; i++) {
        const current = artifacts[i];
        const currentId = current._id.toString();

        if (processed.has(currentId)) continue;

        const group = [currentId];
        processed.add(currentId);

        const vesselId = current.onboard_vessel_id.toString();
        if (!current.portal?.duplication_index) continue; // Temporary — remove after QMW-1121 microservice deployment
        if (!current.portal.duplication_index || current.portal.duplication_index < threshold) {
            groups.push(group);
            continue;
        }

        for (let j = i + 1; j < artifacts.length; j++) {
            const next = artifacts[j];
            const nextId = next._id.toString();
            if (processed.has(nextId) || next.onboard_vessel_id.toString() !== vesselId) continue;
            if (!next.portal?.duplication_index || next.portal.duplication_index === 0) break;
            group.push(nextId);
            processed.add(nextId);
            if (next.portal.duplication_index < threshold) break;
        }
        groups.push(group);
    }

    return groups;
};

function splitByMonthsUTC(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const ranges = [];
    let current = new Date(Date.UTC(start.getUTCFullYear(), start.getUTCMonth(), start.getUTCDate()));

    while (current <= end) {
        // Start of the range
        const rangeStart =
            current.getTime() === Date.UTC(start.getUTCFullYear(), start.getUTCMonth(), start.getUTCDate())
                ? start
                : new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth(), 1, 0, 0, 0, 0));

        // End of the month in UTC
        const monthEnd = new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth() + 1, 0, 23, 59, 59, 999));

        // End of the range (clip to given end)
        const rangeEnd = monthEnd > end ? end : monthEnd;

        ranges.push({
            start: rangeStart.toISOString(),
            end: rangeEnd.toISOString(),
        });

        // Move to the 1st of the next month in UTC
        current = new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth() + 1, 1));
    }

    return ranges;
}

const getSimplifiedCoords = (coords) => {
    // Step 1: Prepare points with a reference index
    const points = coords.map((c, i) => ({
        x: c.location.coordinates[0],
        y: c.location.coordinates[1],
        _index: i, // store original index
    }));

    // Step 2: Simplify
    const simplified = simplify(points, 0.0001, true);

    // Step 3: Map back to original objects
    const result = simplified.map((p) => coords[p._index]);

    return result;
};

function cleanSuggestion(str) {
    return str
        .replace(/[^a-zA-Z0-9\s]/g, " ") // only letters, numbers, spaces
        .replace(/\s+/g, " ") // collapse whitespace
        .trim();
}

const getLocationsCollections = async (locations, startTimestamp, endTimestamp) => {
    const start = dayjs(new Date(startTimestamp)).startOf("month");
    const end = dayjs(new Date(endTimestamp)).startOf("month");

    const collections = [];
    let current = start.clone();

    while (current.isSameOrBefore(end)) {
        const month = current.format("YYYY-MM");
        const collectionExists = (await locations.db.listCollections({ name: month }).toArray()).length > 0;
        current = current.add(1, "month");
        if (collectionExists) collections.push(locations.collection(month));
    }

    return collections;
};

module.exports = {
    isURL,
    isBase64,
    isBase64OrURL,
    validateError,
    isIntStrict,
    getSessionsByCoordinates,
    generateInvitationLink,
    generateUnsubscribeToken,
    canAccessVessel,
    generateZip,
    removeSpecialCharsFromFilename,
    fileNameTimestamp,
    userHasPermissions,
    streamToBuffer,
    getStaticMapOld,
    buildStaticMarkerSignature,
    generateTimeSeries,
    escapeRegExp,
    getUnitIdsFromVessel,
    getTimestampsFromVessel,
    getContentTypeFromFileExtension,
    normalizeName,
    findVesselByUnitHistory,
    groupArtifactsByDuplicateIndex,
    cleanSuggestion,
    splitByMonthsUTC,
    getSimplifiedCoords,
    getLocationsCollections,
};
