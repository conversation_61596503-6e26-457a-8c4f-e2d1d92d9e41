require("dotenv").config();
require("aws-sdk/lib/maintenance_mode_message").suppress = true;
require("./modules/processLogs");
const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const path = require("path");
const socketIo = require("socket.io");
const http = require("http");
const ioEmitter = require("./modules/ioEmitter");
const jwt = require("jsonwebtoken");
const SessionLog = require("./models/SessionLog");
const { swaggerUi, swaggerDocs, swaggerConfig } = require("./modules/swagger");
const cookieParser = require("cookie-parser");
const { v4: uuidv4 } = require("uuid");
const redisClient = require("./services/Redis.service");

const app = express();
const server = http.createServer(app);
const io = new socketIo.Server(server, { cors: { origin: "*" } });

app.use(
    cors({
        exposedHeaders: ["RateLimit-Reset", "Content-Disposition", "Content-Type"],
        origin: true,
        credentials: true,
    }),
);

app.use(cookieParser());
app.use(express.json({ limit: "20mb" }));

app.use("/api", (req, res, next) => {
    if (!req.headers.authorization) {
        console.log(`[${req.method}: ${req.originalUrl}] user: public, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `);
    }
    if (!req.cookies.deviceId) {
        const deviceId = uuidv4();
        res.cookie("deviceId", deviceId, {
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
        });
    }
    next();
});
app.use("/api/docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs, swaggerConfig));
app.use("/api", require("./routes/index")); // this is a v1 version route
app.use("/api/v2", require("./routes/v2/index.v2")); // this is a v2 version route

app.use("/api/*", (req, res) => res.status(404).send(`< h3 > Sorry, that route does not exist.</h3 > `));
app.use("/api", (req, res) => res.send(`< h3 > Welcome to Quartermaster API</h3 > `));

app.use(express.static(path.join(__dirname, "frontend", "dist")));

app.get("*", (req, res) => {
    res.sendFile(path.join(__dirname, "frontend", "dist", "index.html"));
});

const PORT = Number(process.env.PORT);

// mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
//     .then(() => {
//         server.listen(PORT, () => {
//             console.log(`Server running on port ${ PORT } `);
//         });
//     })
//     .catch(err => console.log(err));

if (process.env.NODE_ENV !== "test") {
    const rServer = server.listen(PORT, () => {
        console.log(`Server running at http://localhost:${PORT}`);
    });

    rServer.setTimeout(600000);
}

io.use((socket, next) => {
    try {
        const { jwt_token } = socket.handshake.auth;
        if (!jwt_token) return next(new Error("Authentication error: No token provided"));

        const { user_id } = jwt.verify(jwt_token, process.env.JWT_SECRET);
        if (!user_id) return next(new Error("Authentication error: Invalid token"));

        socket.handshake.auth.user_id = user_id;

        next();
    } catch (err) {
        next(new Error(err.message));
    }
});

io.on("connection", async (socket) => {
    console.log(`User connected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
    socket.on("disconnect", async () => {
        console.log(`User disconnected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);

        await SessionLog.updateOne({ socket_id: socket.id }, { $set: { disconnect_timestamp: new Date().toISOString() } });
    });

    SessionLog.create({
        socket_id: socket.id,
        device: socket.handshake.auth.device,
        browser: socket.handshake.auth.browser,
        user_id: mongoose.Types.ObjectId(socket.handshake.auth.user_id),
    });
});

ioEmitter.on("notifyAll", (event) => {
    io.emit(event.name, event.data);
});

process.on("uncaughtException", (err) => {
    console.error("(FATAL ERROR) Uncaught Exception:", err);
    console.error(err);
});

module.exports = app;
module.exports.server = server;
module.exports.io = io;
module.exports.redisClient = redisClient;

// test: verify autoscale ci
// test: verify autoscale ci
// test: verify autoscale ci
// test: verify autoscale ci
// test: verify staging ci